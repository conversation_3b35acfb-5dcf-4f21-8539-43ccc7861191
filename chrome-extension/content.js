// Track the current URL to detect navigation changes
let currentUrl = window.location.href;
let lastKnownPhotoId = null;

function isPhotoPage() {
  // More robust detection for photo pages
  return window.location.pathname.includes('/photo/') ||
         window.location.hash.includes('/photo/') ||
         document.querySelector('[data-photo-id]') !== null ||
         document.querySelector('button[aria-label="Share"]') !== null;
}

function getCurrentPhotoId() {
  // Try multiple methods to get the photo ID
  const pathMatch = window.location.pathname.match(/\/photo\/([^\/]+)/);
  if (pathMatch) return pathMatch[1];

  const hashMatch = window.location.hash.match(/\/photo\/([^\/]+)/);
  if (hashMatch) return hashMatch[1];

  // Try to get from data attributes
  const photoElement = document.querySelector('[data-photo-id]');
  if (photoElement) return photoElement.getAttribute('data-photo-id');

  return null;
}

function createCompressButton() {
  console.log("Creating compress button...");

  const shareButton = document.querySelector('button[aria-label="Share"]');
  if (!shareButton) {
    console.log("Share button not found yet.");
    return false;
  }

  const originalWrapper = shareButton.closest('.DNAsC.G6iPcb');
  if (!originalWrapper) {
    console.log("Could not find the button's wrapper.");
    return false;
  }

  const newButtonWrapper = originalWrapper.cloneNode(true);
  newButtonWrapper.setAttribute('data-vidcompressor-wrapper', 'true');

  const buttonToReplace = newButtonWrapper.querySelector('button');
  if (!buttonToReplace) {
    console.log("Could not find button in cloned wrapper.");
    return false;
  }

  buttonToReplace.id = 'compress-button';
  buttonToReplace.setAttribute('aria-label', 'Compress');

  const tooltipId = 'tt-compress-' + Date.now();
  buttonToReplace.setAttribute('data-tooltip-id', tooltipId);

  const icon = newButtonWrapper.querySelector('svg');
  if (icon) {
    icon.innerHTML = `
      <path d="M15 9L20 4M20 4H16M20 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9 9L4 4M4 4H8M4 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M15 15L20 20M20 20H16M20 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9 15L4 20M4 20H8M4 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    `;
  }

  const tooltip = newButtonWrapper.querySelector('div[role="tooltip"]');
  if (tooltip) {
    tooltip.id = tooltipId;
    tooltip.textContent = 'Compress';
  }

  buttonToReplace.onclick = (e) => {
    e.stopPropagation();
    e.preventDefault();
    const videoId = getCurrentPhotoId();
    if (!videoId) {
      alert('Could not determine video ID');
      return;
    }
    chrome.runtime.sendMessage({ action: "compressVideo", videoId: videoId }, function(response) {
      if (response.success) {
        alert('Compression started!');
      } else {
        alert('Error: ' + response.message);
      }
    });
  };

  originalWrapper.parentElement.insertBefore(newButtonWrapper, originalWrapper);
  console.log("Compress button added successfully.");
  return true;
}

function removeCompressButton() {
  const compressButton = document.getElementById('compress-button');
  if (compressButton) {
    console.log("Removing compress button.");
    const wrapper = compressButton.closest('div[data-vidcompressor-wrapper]');
    if (wrapper) {
      wrapper.remove();
    } else {
      compressButton.remove();
    }
  }
}

function manageCompressButton() {
  const currentPhotoId = getCurrentPhotoId();
  const isOnPhotoPage = isPhotoPage();
  const compressButton = document.getElementById('compress-button');

  console.log(`Photo page: ${isOnPhotoPage}, Photo ID: ${currentPhotoId}, Button exists: ${!!compressButton}`);

  // If we're not on a photo page, remove the button
  if (!isOnPhotoPage) {
    if (compressButton) {
      removeCompressButton();
    }
    lastKnownPhotoId = null;
    return;
  }

  // If we're on a photo page but the photo ID changed, remove the old button
  if (currentPhotoId && currentPhotoId !== lastKnownPhotoId) {
    console.log(`Photo changed from ${lastKnownPhotoId} to ${currentPhotoId}`);
    if (compressButton) {
      removeCompressButton();
    }
    lastKnownPhotoId = currentPhotoId;
  }

  // If we're on a photo page and don't have a button, try to add it
  if (isOnPhotoPage && !document.getElementById('compress-button')) {
    createCompressButton();
  }
}

// Use MutationObserver to detect DOM changes more efficiently
const observer = new MutationObserver((mutations) => {
  let shouldCheck = false;

  mutations.forEach((mutation) => {
    // Check if the URL changed
    if (currentUrl !== window.location.href) {
      currentUrl = window.location.href;
      shouldCheck = true;
      console.log("URL changed:", currentUrl);
    }

    // Check if toolbar elements were added/removed
    if (mutation.type === 'childList') {
      const hasToolbarChanges = Array.from(mutation.addedNodes).some(node =>
        node.nodeType === Node.ELEMENT_NODE &&
        (node.querySelector && node.querySelector('button[aria-label="Share"]'))
      ) || Array.from(mutation.removedNodes).some(node =>
        node.nodeType === Node.ELEMENT_NODE &&
        (node.querySelector && node.querySelector('button[aria-label="Share"]'))
      );

      if (hasToolbarChanges) {
        shouldCheck = true;
        console.log("Toolbar changes detected");
      }
    }
  });

  if (shouldCheck) {
    // Small delay to let the DOM settle
    setTimeout(manageCompressButton, 100);
  }
});

// Start observing
observer.observe(document.body, {
  childList: true,
  subtree: true
});

// Also run periodically as a fallback
setInterval(manageCompressButton, 2000);

// Run immediately
manageCompressButton();
