function manageCompressButton() {
  console.log("--- Tick ---");
  const isPhotoPage = window.location.pathname.includes('/photo/');
  const compressButton = document.getElementById('compress-button');

  console.log(`isPhotoPage: ${isPhotoPage}, compressButton exists: ${!!compressButton}`);

  if (!isPhotoPage) {
    if (compressButton) {
      console.log("Not a photo page, removing button.");
      const wrapper = compressButton.closest('div[data-vidcompressor-wrapper]');
      if (wrapper) {
        wrapper.remove();
      } else {
        compressButton.remove();
      }
    }
    return;
  }

  if (compressButton) {
    console.log("Button already exists, doing nothing.");
    return;
  }

  console.log("On photo page and button doesn't exist. Trying to add it.");
  const shareButton = document.querySelector('button[aria-label="Share"]');
  if (!shareButton) {
    console.log("Share button not found yet.");
    return;
  }
  console.log("Share button found.");

  const originalWrapper = shareButton.closest('.DNAsC.G6iPcb');
  if (!originalWrapper) {
    console.log("Could not find the button's wrapper.");
    return;
  }
  console.log("Original wrapper found.");

  const newButtonWrapper = originalWrapper.cloneNode(true);
  newButtonWrapper.setAttribute('data-vidcompressor-wrapper', 'true');

  const buttonToReplace = newButtonWrapper.querySelector('button');
  if (!buttonToReplace) {
    console.log("Could not find button in cloned wrapper.");
    return;
  }

  buttonToReplace.id = 'compress-button';
  buttonToReplace.setAttribute('aria-label', 'Compress');

  const tooltipId = 'tt-compress-' + Date.now();
  buttonToReplace.setAttribute('data-tooltip-id', tooltipId);

  const icon = newButtonWrapper.querySelector('svg');
  if (icon) {
    icon.innerHTML = `
      <path d="M15 9L20 4M20 4H16M20 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9 9L4 4M4 4H8M4 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M15 15L20 20M20 20H16M20 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9 15L4 20M4 20H8M4 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    `;
  }

  const tooltip = newButtonWrapper.querySelector('div[role="tooltip"]');
  if (tooltip) {
    tooltip.id = tooltipId;
    tooltip.textContent = 'Compress';
  }

  originalWrapper.parentElement.insertBefore(newButtonWrapper, originalWrapper);
  console.log("Button supposedly added.");

  buttonToReplace.onclick = (e) => {
    e.stopPropagation();
    e.preventDefault();
    const videoId = window.location.pathname.split('/')[2];
    if (!videoId) return;
    chrome.runtime.sendMessage({ action: "compressVideo", videoId: videoId }, function(response) {
      if (response.success) {
        alert('Compression started!');
      } else {
        alert('Error: ' + response.message);
      }
    });
  };
}

setInterval(manageCompressButton, 1000);
