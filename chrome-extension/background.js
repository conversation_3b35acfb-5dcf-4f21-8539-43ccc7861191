chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "compressVideo") {
    chrome.identity.getAuthToken({ interactive: true }, function(token) {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError);
        sendResponse({ success: false, message: "Could not authenticate." });
        return;
      }

      fetch(`http://localhost:5000/api/videos/${request.videoId}/compress`, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + token,
          'Content-Type': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        sendResponse({ success: true, message: data.message });
      })
      .catch(error => {
        console.error('Error:', error);
        sendResponse({ success: false, message: 'Error compressing video: ' + error.message });
      });
    });
    return true; // Indicates that the response is sent asynchronously
  }
});
